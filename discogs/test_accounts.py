#!/usr/bin/env python3
"""
Discogs账号Token验证测试脚本
用于快速验证账号状态，不使用代理
"""

import requests
import time

# 测试账号配置
TEST_ACCOUNTS = [
    {
        'email': '<EMAIL>',
        'token': 'KzOovyQCbHcFmnhUNjigyNCezgquqQYvTiQAgXsa',
        'user_agent': 'DiscogsAPIClient/1.0'
    },
    {
        'email': '<EMAIL>',
        'token': 'dYeSySUWrbjxXgVQbVNgKazNbrIRMHFcZgkpnlvj',
        'user_agent': 'DiscogsAPIClient/1.0'
    }
]

def test_account_token(account):
    """测试单个账号的token有效性"""
    print(f"\n🔍 测试账号: {account['email']}")
    
    headers = {
        'User-Agent': account['user_agent'],
        'Authorization': f'Discogs token={account["token"]}'
    }
    
    # 测试用户信息API（不需要特定资源ID）
    test_url = 'https://api.discogs.com/oauth/identity'
    
    try:
        print(f"📡 请求URL: {test_url}")
        response = requests.get(test_url, headers=headers, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 账号验证成功!")
            print(f"   - 用户名: {data.get('username', 'N/A')}")
            print(f"   - 用户ID: {data.get('id', 'N/A')}")
            print(f"   - 资源URL: {data.get('resource_url', 'N/A')}")
            return True
        elif response.status_code == 401:
            print(f"❌ 账号认证失败: Token无效或已过期")
            return False
        elif response.status_code == 429:
            print(f"⏳ 请求频率限制: 需要等待")
            return False
        else:
            print(f"⚠️ 未知响应: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
            return False
            
    except requests.exceptions.SSLError as e:
        print(f"🔒 SSL错误: {e}")
        print("   这表明SSL连接问题，可能是网络环境或代理问题")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"🌐 连接错误: {e}")
        return False
    except requests.exceptions.Timeout as e:
        print(f"⏰ 请求超时: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_direct_connection():
    """测试直连api.discogs.com的基础连接"""
    print(f"\n🔗 测试直连api.discogs.com...")
    
    try:
        # 简单的GET请求测试连接
        response = requests.get('https://api.discogs.com/', timeout=30)
        print(f"✅ 直连测试成功: {response.status_code}")
        return True
    except requests.exceptions.SSLError as e:
        print(f"🔒 直连SSL错误: {e}")
        print("   这表明本地网络环境的SSL配置问题")
        return False
    except Exception as e:
        print(f"❌ 直连失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Discogs账号和连接测试...")
    print("=" * 50)
    
    # 测试直连
    direct_ok = test_direct_connection()
    
    # 测试账号
    account_results = []
    for account in TEST_ACCOUNTS:
        result = test_account_token(account)
        account_results.append((account['email'], result))
        time.sleep(2)  # 避免频率限制
    
    # 总结结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print(f"🔗 直连测试: {'✅ 成功' if direct_ok else '❌ 失败'}")
    
    for email, result in account_results:
        status = '✅ 有效' if result else '❌ 无效'
        print(f"👤 {email}: {status}")
    
    # 建议
    print("\n💡 建议:")
    if not direct_ok:
        print("   - 直连失败，可能是网络环境或SSL配置问题")
        print("   - 检查防火墙、DNS设置")
        print("   - 尝试更新SSL证书或Python版本")
    elif not any(result for _, result in account_results):
        print("   - 所有账号验证失败，可能是token问题")
        print("   - 检查token是否正确或已过期")
    else:
        print("   - 基础连接正常，问题可能在代理配置")

if __name__ == "__main__":
    main()
