#!/usr/bin/env python3
"""
代理连接测试脚本
用于诊断代理服务器的HTTPS连接能力
"""

import requests
import time
from dataclasses import dataclass

@dataclass
class ProxyConfig:
    http: str
    https: str
    name: str

# 代理配置
PROXY_CONFIG = ProxyConfig(
    http='http://127.0.0.1:7897',
    https='http://127.0.0.1:7897',
    name='proxy_7897'
)

def test_proxy_basic_connection():
    """测试代理的基础连接能力"""
    print(f"🔍 测试代理基础连接: {PROXY_CONFIG.name}")
    
    proxies = {
        'http': PROXY_CONFIG.http,
        'https': PROXY_CONFIG.https
    }
    
    # 测试HTTP连接
    try:
        print("📡 测试HTTP连接...")
        response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=30)
        if response.status_code == 200:
            print(f"✅ HTTP代理连接成功")
            print(f"   代理IP: {response.json().get('origin', 'N/A')}")
        else:
            print(f"⚠️ HTTP代理响应异常: {response.status_code}")
    except Exception as e:
        print(f"❌ HTTP代理连接失败: {e}")
        return False
    
    # 测试HTTPS连接
    try:
        print("🔒 测试HTTPS连接...")
        response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=30)
        if response.status_code == 200:
            print(f"✅ HTTPS代理连接成功")
            print(f"   代理IP: {response.json().get('origin', 'N/A')}")
            return True
        else:
            print(f"⚠️ HTTPS代理响应异常: {response.status_code}")
            return False
    except requests.exceptions.SSLError as e:
        print(f"🔒 HTTPS代理SSL错误: {e}")
        print("   代理服务器可能不支持HTTPS或SSL配置有问题")
        return False
    except Exception as e:
        print(f"❌ HTTPS代理连接失败: {e}")
        return False

def test_proxy_discogs_connection():
    """测试代理连接Discogs API的能力"""
    print(f"\n🎵 测试代理连接Discogs API...")
    
    proxies = {
        'http': PROXY_CONFIG.http,
        'https': PROXY_CONFIG.https
    }
    
    # 测试Discogs API基础连接
    try:
        print("📡 测试api.discogs.com连接...")
        response = requests.get('https://api.discogs.com/', proxies=proxies, timeout=30)
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ 代理连接Discogs API成功")
            return True
        else:
            print(f"⚠️ Discogs API响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.SSLError as e:
        print(f"🔒 Discogs API SSL错误: {e}")
        print("   这是导致原始错误的根本原因!")
        return False
    except Exception as e:
        print(f"❌ Discogs API连接失败: {e}")
        return False

def test_proxy_with_ssl_options():
    """测试不同SSL配置下的代理连接"""
    print(f"\n🔧 测试不同SSL配置...")
    
    proxies = {
        'http': PROXY_CONFIG.http,
        'https': PROXY_CONFIG.https
    }
    
    # 测试1: 禁用SSL验证
    try:
        print("🔒 测试1: 禁用SSL验证...")
        response = requests.get(
            'https://api.discogs.com/', 
            proxies=proxies, 
            verify=False,  # 禁用SSL验证
            timeout=30
        )
        print(f"✅ 禁用SSL验证成功: {response.status_code}")
    except Exception as e:
        print(f"❌ 禁用SSL验证仍失败: {e}")
    
    # 测试2: 使用不同的SSL适配器
    try:
        print("🔒 测试2: 使用SSL适配器...")
        session = requests.Session()
        
        # 配置SSL适配器
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        response = session.get(
            'https://api.discogs.com/', 
            proxies=proxies, 
            timeout=30
        )
        print(f"✅ SSL适配器成功: {response.status_code}")
        
    except Exception as e:
        print(f"❌ SSL适配器失败: {e}")

def test_direct_vs_proxy():
    """对比直连和代理连接的差异"""
    print(f"\n⚖️ 对比直连 vs 代理连接...")
    
    # 直连测试
    try:
        print("🔗 直连测试...")
        response = requests.get('https://api.discogs.com/', timeout=30)
        print(f"✅ 直连成功: {response.status_code}")
        direct_ok = True
    except Exception as e:
        print(f"❌ 直连失败: {e}")
        direct_ok = False
    
    # 代理测试
    proxies = {
        'http': PROXY_CONFIG.http,
        'https': PROXY_CONFIG.https
    }
    
    try:
        print("🔀 代理测试...")
        response = requests.get('https://api.discogs.com/', proxies=proxies, timeout=30)
        print(f"✅ 代理成功: {response.status_code}")
        proxy_ok = True
    except Exception as e:
        print(f"❌ 代理失败: {e}")
        proxy_ok = False
    
    # 结论
    if direct_ok and not proxy_ok:
        print("💡 结论: 直连正常，代理有问题")
        print("   建议: 检查代理服务器的HTTPS支持")
    elif not direct_ok and not proxy_ok:
        print("💡 结论: 网络环境问题")
        print("   建议: 检查网络连接和SSL配置")
    elif direct_ok and proxy_ok:
        print("💡 结论: 连接都正常，可能是间歇性问题")
    else:
        print("💡 结论: 异常情况，需要进一步诊断")

def main():
    """主测试函数"""
    print("🚀 开始代理连接诊断...")
    print("=" * 50)
    
    # 基础代理测试
    basic_ok = test_proxy_basic_connection()
    
    # Discogs API测试
    discogs_ok = test_proxy_discogs_connection()
    
    # SSL配置测试
    test_proxy_with_ssl_options()
    
    # 对比测试
    test_direct_vs_proxy()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断结果总结:")
    print(f"🔗 代理基础连接: {'✅ 正常' if basic_ok else '❌ 异常'}")
    print(f"🎵 Discogs API连接: {'✅ 正常' if discogs_ok else '❌ 异常'}")
    
    print("\n💡 建议:")
    if not basic_ok:
        print("   - 代理服务器本身有问题，检查代理配置")
        print("   - 确认代理服务器是否运行在127.0.0.1:7897")
    elif not discogs_ok:
        print("   - 代理不支持HTTPS或与Discogs API不兼容")
        print("   - 考虑更换代理服务器或配置")
        print("   - 尝试使用直连模式")

if __name__ == "__main__":
    main()
