import requests
import time
import csv
import os
from dataclasses import dataclass
from pymongo import MongoClient

# 代理配置类
@dataclass
class ProxyConfig:
    """代理配置类"""
    http: str
    https: str
    name: str
    is_active: bool = True
    failure_count: int = 0
    last_check_time: float = 0.0

# 代理配置
PROXY_CONFIGS = [
    ProxyConfig(
        http='http://127.0.0.1:7897',
        https='http://127.0.0.1:7897',
        name='proxy_7897'
    ),
]

DISCOGS_ACCOUNTS = [
    {
        'email': '<EMAIL>',
        'token': 'KzOovyQCbHcFmnhUNjigyNCezgquqQYvTiQAgXsa',
        'user_agent': 'DiscogsAPIClient/1.0'
    },
    {
        'email': '<EMAIL>',
        'token': 'dYeSySUWrbjxXgVQbVNgKazNbrIRMHFcZgkpnlvj',
        'user_agent': 'DiscogsAPIClient/1.0'
    }
]

# 数据库配置
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

# 查询配置
TARGET_ID_RANGE = (100000, 100201)  # 目标ID范围
CSV_PATH = 'discogs_releases_dynamic.csv'  # 动态CSV文件名，稍后会更新
WAIT_TIME = 1.05  # 控制请求速率，防止被限速

# 账号轮换计数器
account_counter = 0
# 代理轮换计数器
proxy_counter = 0

def get_current_account():
    """获取当前轮换的账号信息"""
    global account_counter
    account = DISCOGS_ACCOUNTS[account_counter % len(DISCOGS_ACCOUNTS)]
    account_counter += 1
    return account

def get_current_proxy():
    """获取当前轮换的代理信息"""
    global proxy_counter
    if not PROXY_CONFIGS:
        return None
    proxy = PROXY_CONFIGS[proxy_counter % len(PROXY_CONFIGS)]
    proxy_counter += 1
    return proxy

def get_headers(account):
    """根据账号信息生成请求头"""
    return {
        'User-Agent': account['user_agent'],
        'Authorization': f'Discogs token={account["token"]}'
    }

def get_proxies(proxy_config):
    """根据代理配置生成代理字典"""
    if proxy_config is None:
        return None
    return {
        'http': proxy_config.http,
        'https': proxy_config.https
    }

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        print(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        raise

def get_existing_ids_from_table(db, table_name, id_range=None):
    """从指定表中获取现有的ID集合"""
    try:
        collection = db[table_name]
        query = {}

        # 如果指定了ID范围，添加范围查询
        if id_range:
            start_id, end_id = id_range
            query = {'id': {'$gte': start_id, '$lt': end_id}}

        # 查询ID字段，排除_id
        cursor = collection.find(query, {'id': 1, '_id': 0})
        existing_ids = set()

        for doc in cursor:
            if 'id' in doc:
                try:
                    # 确保ID为整数类型
                    id_value = int(doc['id']) if isinstance(doc['id'], str) else doc['id']
                    existing_ids.add(id_value)
                except (ValueError, TypeError):
                    continue

        print(f"📊 从 {table_name} 表获取到 {len(existing_ids)} 个ID")
        return existing_ids
    except Exception as e:
        print(f"⚠️ 从 {table_name} 表查询ID失败: {e}")
        return set()

def find_missing_ids_in_range(db, start_id, end_id):
    """查找指定范围内缺失的release ID"""
    print(f"🔍 开始查找范围 {start_id}-{end_id-1} 内的缺失ID...")

    # 生成目标ID范围
    target_ids = set(range(start_id, end_id))
    print(f"📊 目标ID范围: {len(target_ids)} 个ID")

    # 获取各表中已存在的ID
    release_new_ids = get_existing_ids_from_table(db, 'release_new', (start_id, end_id))
    # release_copy_ids = get_existing_ids_from_table(db, 'release_copy', (start_id, end_id))
    release_404_ids = get_existing_ids_from_table(db, 'release_404', (start_id, end_id))

    # 计算已处理的ID（存在于任一表中）
    # processed_ids = release_new_ids | release_copy_ids | release_404_ids
    processed_ids = release_new_ids | release_404_ids

    print(f"📊 已处理的ID总数: {len(processed_ids)} 个")

    # 计算缺失的ID
    missing_ids = target_ids - processed_ids
    missing_ids_list = sorted(list(missing_ids))

    print(f"✅ 找到 {len(missing_ids_list)} 个缺失的ID需要处理")
    return missing_ids_list

# 主程序逻辑
def main():
    """主程序入口"""
    try:
        # 连接数据库
        print("🔗 正在连接数据库...")
        client, db = connect_to_mongodb()

        # 查找缺失的ID
        start_id, end_id = TARGET_ID_RANGE
        missing_ids = find_missing_ids_in_range(db, start_id, end_id)

        if not missing_ids:
            print("✅ 没有发现缺失的ID，所有数据都已存在")
            return

        # 更新CSV文件名
        global CSV_PATH
        CSV_PATH = f'discogs_releases_{missing_ids[0]}_to_{missing_ids[-1]}.csv'
        print(f"📁 输出文件: {CSV_PATH}")

        # 处理缺失的ID
        print(f"🚀 开始处理 {len(missing_ids)} 个缺失的release ID...")

        with open(CSV_PATH, 'w', newline='', encoding='utf-8') as csvfile:
            writer = None
            processed_count = 0
            success_count = 0

            for rid in missing_ids:
                # 获取当前轮换的账号和代理
                current_account = get_current_account()
                current_proxy = get_current_proxy()
                headers = get_headers(current_account)
                proxies = get_proxies(current_proxy)

                # 构建日志信息
                proxy_info = f"代理: {current_proxy.name}" if current_proxy else "代理: 无"
                account_info = f"账号: {current_account['email']}"

                url = f'https://api.discogs.com/releases/{rid}'
                try:
                    response = requests.get(url, headers=headers, proxies=proxies)
                    if response.status_code == 404:
                        print(f"❌ Release {rid} not found. ({account_info}, {proxy_info})")
                        processed_count += 1
                        continue
                    response.raise_for_status()
                    data = response.json()

                    # 初始化字段并写入表头
                    if writer is None:
                        writer = csv.DictWriter(csvfile, fieldnames=data.keys())
                        writer.writeheader()

                    writer.writerow(data)
                    success_count += 1
                    print(f"✅ Release {rid} fetched: {data.get('title', 'N/A')} ({account_info}, {proxy_info})")
                except Exception as e:
                    print(f"⚠️ Error fetching release {rid}: {e} ({account_info}, {proxy_info})")

                processed_count += 1
                time.sleep(WAIT_TIME)

                # 显示进度
                if processed_count % 10 == 0:
                    print(f"📊 进度: {processed_count}/{len(missing_ids)} ({processed_count/len(missing_ids)*100:.1f}%)")

        print(f"🎉 处理完成！成功获取 {success_count} 条记录，总处理 {processed_count} 个ID")

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
    finally:
        # 关闭数据库连接
        if 'client' in locals():
            client.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
