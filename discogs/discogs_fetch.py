import requests
import time
import csv
from dataclasses import dataclass

# 代理配置类
@dataclass
class ProxyConfig:
    """代理配置类"""
    http: str
    https: str
    name: str
    is_active: bool = True
    failure_count: int = 0
    last_check_time: float = 0.0

# 代理配置
PROXY_CONFIGS = [
    ProxyConfig(
        http='http://127.0.0.1:7897',
        https='http://127.0.0.1:7897',
        name='proxy_7897'
    ),
]

DISCOGS_ACCOUNTS = [
    {
        'email': '<EMAIL>',
        'token': 'KzOovyQCbHcFmnhUNjigyNCezgquqQYvTiQAgXsa',
        'user_agent': 'DiscogsAPIClient/1.0'
    },
    {
        'email': '<EMAIL>',
        'token': 'dYeSySUWrbjxXgVQbVNgKazNbrIRMHFcZgkpnlvj',
        'user_agent': 'DiscogsAPIClient/1.0'
    }
]

CSV_PATH = 'discogs_releases_100000_100200.csv'
WAIT_TIME = 1.05  # 控制请求速率，防止被限速

# 账号轮换计数器
account_counter = 0
# 代理轮换计数器
proxy_counter = 0

def get_current_account():
    """获取当前轮换的账号信息"""
    global account_counter
    account = DISCOGS_ACCOUNTS[account_counter % len(DISCOGS_ACCOUNTS)]
    account_counter += 1
    return account

def get_current_proxy():
    """获取当前轮换的代理信息"""
    global proxy_counter
    if not PROXY_CONFIGS:
        return None
    proxy = PROXY_CONFIGS[proxy_counter % len(PROXY_CONFIGS)]
    proxy_counter += 1
    return proxy

def get_headers(account):
    """根据账号信息生成请求头"""
    return {
        'User-Agent': account['user_agent'],
        'Authorization': f'Discogs token={account["token"]}'
    }

def get_proxies(proxy_config):
    """根据代理配置生成代理字典"""
    if proxy_config is None:
        return None
    return {
        'http': proxy_config.http,
        'https': proxy_config.https
    }

with open(CSV_PATH, 'w', newline='', encoding='utf-8') as csvfile:
    writer = None
    for rid in range(100000, 100201):
        # 获取当前轮换的账号和代理
        current_account = get_current_account()
        current_proxy = get_current_proxy()
        headers = get_headers(current_account)
        proxies = get_proxies(current_proxy)

        # 构建日志信息
        proxy_info = f"代理: {current_proxy.name}" if current_proxy else "代理: 无"
        account_info = f"账号: {current_account['email']}"

        url = f'https://api.discogs.com/releases/{rid}'
        try:
            response = requests.get(url, headers=headers, proxies=proxies)
            if response.status_code == 404:
                print(f"❌ Release {rid} not found. ({account_info}, {proxy_info})")
                continue
            response.raise_for_status()
            data = response.json()

            # 初始化字段并写入表头
            if writer is None:
                writer = csv.DictWriter(csvfile, fieldnames=data.keys())
                writer.writeheader()

            writer.writerow(data)
            print(f"✅ Release {rid} fetched: {data.get('title', 'N/A')} ({account_info}, {proxy_info})")
        except Exception as e:
            print(f"⚠️ Error fetching release {rid}: {e} ({account_info}, {proxy_info})")
        time.sleep(WAIT_TIME)
