import requests
import time
import os
from dataclasses import dataclass
from pymongo import MongoClient
from datetime import datetime, timezone

# 导入枚举类
try:
    from release.enums import Permissions, Source
except ImportError:
    # 如果导入失败，定义本地枚举
    class Permissions:
        ALL_VISIBLE = 1
    class Source:
        DISCOGS = 1

# 代理配置类
@dataclass
class ProxyConfig:
    """代理配置类"""
    http: str
    https: str
    name: str
    is_active: bool = True
    failure_count: int = 0
    last_check_time: float = 0.0

# 代理配置
PROXY_CONFIGS = [
    ProxyConfig(
        http='http://127.0.0.1:7897',
        https='http://127.0.0.1:7897',
        name='proxy_7897'
    ),
]

DISCOGS_ACCOUNTS = [
    {
        'email': '<EMAIL>',
        'token': 'KzOovyQCbHcFmnhUNjigyNCezgquqQYvTiQAgXsa',
        'user_agent': 'DiscogsAPIClient/1.0'
    },
    {
        'email': '<EMAIL>',
        'token': 'dYeSySUWrbjxXgVQbVNgKazNbrIRMHFcZgkpnlvj',
        'user_agent': 'DiscogsAPIClient/1.0'
    }
]

# 数据库配置
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')

# 查询配置
TARGET_ID_RANGE = (100000, 100300)  # 目标ID范围
WAIT_TIME = 1.05  # 控制请求速率，防止被限速

DISCOGS_URL = 'https://api.discogs.com/releases/'

# 账号轮换计数器
account_counter = 0
# 代理轮换计数器
proxy_counter = 0

def get_current_account():
    """获取当前轮换的账号信息"""
    global account_counter
    account = DISCOGS_ACCOUNTS[account_counter % len(DISCOGS_ACCOUNTS)]
    account_counter += 1
    return account

def get_current_proxy():
    """获取当前轮换的代理信息"""
    global proxy_counter
    if not PROXY_CONFIGS:
        return None
    proxy = PROXY_CONFIGS[proxy_counter % len(PROXY_CONFIGS)]
    proxy_counter += 1
    return proxy

def get_headers(account):
    """根据账号信息生成请求头"""
    return {
        'User-Agent': account['user_agent'],
        'Authorization': f'Discogs token={account["token"]}'
    }

def get_proxies(proxy_config):
    """根据代理配置生成代理字典"""
    if proxy_config is None:
        return None
    return {
        'http': proxy_config.http,
        'https': proxy_config.https
    }

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        db = client[DB_NAME]
        print(f"✅ 成功连接到MongoDB: {DB_NAME}")
        return client, db
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        raise

def get_existing_ids_from_table(db, table_name, id_range=None):
    """从指定表中获取现有的ID集合"""
    try:
        collection = db[table_name]
        query = {}

        # 如果指定了ID范围，添加范围查询
        if id_range:
            start_id, end_id = id_range
            query = {'id': {'$gte': start_id, '$lt': end_id}}

        # 查询ID字段，排除_id
        cursor = collection.find(query, {'id': 1, '_id': 0})
        existing_ids = set()

        for doc in cursor:
            if 'id' in doc:
                try:
                    # 确保ID为整数类型
                    id_value = int(doc['id']) if isinstance(doc['id'], str) else doc['id']
                    existing_ids.add(id_value)
                except (ValueError, TypeError):
                    continue

        print(f"📊 从 {table_name} 表获取到 {len(existing_ids)} 个ID")
        return existing_ids
    except Exception as e:
        print(f"⚠️ 从 {table_name} 表查询ID失败: {e}")
        return set()

def find_missing_ids_in_range(db, start_id, end_id):
    """
    查找指定范围内缺失的release ID

    逻辑：
    1. 从 release_new 表中查找在 (start_id, end_id) 范围内存在的ID
    2. 从 release_404 表中查找在该范围内存在的ID
    3. 计算目标范围内不存在于上述两个表中的ID
    4. 这些ID就是需要通过API获取的缺失ID
    """
    print(f"🔍 开始查找范围 {start_id}-{end_id-1} 内的缺失ID...")

    # 生成目标ID范围
    target_ids = set(range(start_id, end_id))
    print(f"📊 目标ID范围: {len(target_ids)} 个ID")

    # 获取 release_new 表中已存在的ID
    release_new_ids = get_existing_ids_from_table(db, 'release_new', (start_id, end_id))

    # 获取 release_404 表中已存在的ID（这些ID已确认不存在，无需再次请求）
    release_404_ids = get_existing_ids_from_table(db, 'release_404', (start_id, end_id))

    # 计算已处理的ID（存在于 release_new 或 release_404 表中）
    processed_ids = release_new_ids | release_404_ids
    print(f"📊 已处理的ID总数: {len(processed_ids)} 个")
    print(f"   - release_new 表中: {len(release_new_ids)} 个")
    print(f"   - release_404 表中: {len(release_404_ids)} 个")

    # 计算缺失的ID（目标范围 - 已处理的ID）
    missing_ids = target_ids - processed_ids
    missing_ids_list = sorted(list(missing_ids))

    print(f"✅ 找到 {len(missing_ids_list)} 个缺失的ID需要处理")
    return missing_ids_list

def safe_string_value(value, default=''):
    """安全地处理字符串值"""
    if value is None:
        return default
    return str(value).strip()

def safe_integer_value(value, default=None):
    """安全地处理整数值"""
    if value is None:
        return default
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def convert_api_artists(api_artists):
    """转换API返回的artists数据"""
    if not api_artists:
        return []

    artists = []
    for artist in api_artists:
        artist_doc = {
            'artist_id': safe_integer_value(artist.get('id')),
            'name': safe_string_value(artist.get('name'), ''),
            'role': safe_string_value(artist.get('role'), 'Primary')
        }

        # 添加可选字段
        anv = safe_string_value(artist.get('anv'))
        if anv:
            artist_doc['anv'] = anv

        artists.append(artist_doc)

    return artists

def convert_api_labels(api_labels):
    """转换API返回的labels数据"""
    if not api_labels:
        return []

    labels = []
    for label in api_labels:
        label_doc = {
            'name': safe_string_value(label.get('name'), ''),
            'catno': safe_string_value(label.get('catno'), ''),
            'id': safe_string_value(label.get('id'), '')
        }
        labels.append(label_doc)

    return labels

def convert_api_tracklist(api_tracklist):
    """转换API返回的tracklist数据"""
    if not api_tracklist:
        return []

    tracklist = []
    for track in api_tracklist:
        track_doc = {
            'position': safe_string_value(track.get('position'), ''),
            'title': safe_string_value(track.get('title'), ''),
            'duration': safe_string_value(track.get('duration'), '')
        }
        tracklist.append(track_doc)

    return tracklist

def get_release_table_by_id(db, release_id):
    """
    从release表中获取images等字段
    参考项目中现有的实现模式
    """
    try:
        # 将release_id转换为string类型进行查询
        release_id_str = str(release_id)
        release_doc = db.release.find_one({'id': release_id_str})
        if release_doc:
            return {
                'images': release_doc.get('images', []),
                'notes': release_doc.get('notes', []),
                'identifiers': release_doc.get('identifiers', []),
                'year': release_doc.get('year', None)
            }
        return {
            'images': [],
            'notes': [],
            'identifiers': [],
            'year': None
        }
    except Exception as e:
        print(f"⚠️ 获取数据库字段失败 (release_id: {release_id}): {e}")
        return {
            'images': [],
            'notes': [],
            'identifiers': [],
            'year': None
        }

def convert_api_data_to_document(api_data, db):
    """
    将Discogs API返回的JSON数据转换为数据库文档格式
    参考 api_release_补全器.py 中的转换逻辑
    优化 images 字段获取：优先从数据库获取，备用从API获取
    """
    try:
        release_id = safe_integer_value(api_data.get('id'))

        # 从数据库获取现有数据（优先级1）
        db_fields = get_release_table_by_id(db, release_id)

        # 处理 images 字段：优先数据库，备用API
        db_images = db_fields.get('images', [])
        api_images = api_data.get('images', []) if api_data.get('images') else []

        if db_images and len(db_images) > 0:
            # 使用数据库中的images
            final_images = db_images
            images_source = "数据库"
        else:
            # 回退到API中的images
            final_images = api_images
            images_source = "API"

        print(f"📷 Release {release_id} images来源: {images_source} (数量: {len(final_images)})")

        # 基础字段
        doc = {
            'id': release_id,
            'title': safe_string_value(api_data.get('title'), ''),
            'country': safe_string_value(api_data.get('country'), ''),
            'year': safe_integer_value(api_data.get('year')),
            'master_id': safe_integer_value(api_data.get('master_id')),
            'discogs_status': safe_string_value(api_data.get('status'), 'unknown'),

            # 艺术家信息
            'artists': convert_api_artists(api_data.get('artists', [])),
            'extra_artists': convert_api_artists(api_data.get('extraartists', [])),

            # 标签和公司信息
            'labels': convert_api_labels(api_data.get('labels', [])),
            'companies': api_data.get('companies', []) if api_data.get('companies') else [],

            # 格式和风格信息
            'formats': api_data.get('formats', []) if api_data.get('formats') else [],
            'genres': api_data.get('genres', []) if api_data.get('genres') else [],
            'styles': api_data.get('styles', []) if api_data.get('styles') else [],

            # 其他信息
            'identifiers': api_data.get('identifiers', []) if api_data.get('identifiers') else [],
            'tracklist': convert_api_tracklist(api_data.get('tracklist', [])),
            'images': final_images,  # 使用优化后的images
            'notes': safe_string_value(api_data.get('notes'), ''),

            # 元数据字段
            'source': Source.DISCOGS.value,
            'permissions': Permissions.ALL_VISIBLE.value,
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }

        return doc

    except Exception as e:
        print(f"❌ 数据转换失败: {e}")
        return None

def insert_release_to_database(db, release_doc):
    """
    将release文档插入到数据库中
    包含重复检查逻辑，避免插入已存在的记录
    """
    try:
        collection = db['release_new']
        release_id = release_doc['id']

        # 检查记录是否已存在
        existing_record = collection.find_one({'id': release_id})
        if existing_record:
            print(f"📝 Release {release_id} 已存在于数据库中，跳过插入")
            return False

        # 插入新记录
        result = collection.insert_one(release_doc)
        if result.inserted_id:
            print(f"💾 Release {release_id} 成功保存到数据库")
            return True
        else:
            print(f"❌ Release {release_id} 保存失败")
            return False

    except Exception as e:
        print(f"❌ 数据库插入失败 (Release {release_doc.get('id', 'unknown')}): {e}")
        return False

# 主程序逻辑
def main():
    """主程序入口"""
    try:
        # 连接数据库
        print("🔗 正在连接数据库...")
        client, db = connect_to_mongodb()

        # 查找缺失的ID
        start_id, end_id = TARGET_ID_RANGE
        missing_ids = find_missing_ids_in_range(db, start_id, end_id)

        if not missing_ids:
            print("✅ 没有发现缺失的ID，所有数据都已存在")
            return

        # 处理缺失的ID
        print(f"🚀 开始处理 {len(missing_ids)} 个缺失的release ID...")

        processed_count = 0
        success_count = 0
        saved_count = 0

        for rid in missing_ids:
            # 获取当前轮换的账号和代理
            current_account = get_current_account()
            current_proxy = get_current_proxy()
            headers = get_headers(current_account)
            proxies = get_proxies(current_proxy)

            # 构建日志信息
            proxy_info = f"代理: {current_proxy.name}" if current_proxy else "代理: 无"
            account_info = f"账号: {current_account['email']}"

            url = f'{DISCOGS_URL}{rid}'
            try:
                response = requests.get(url, headers=headers, proxies=proxies)
                if response.status_code == 404:
                    print(f"❌ Release {rid} not found. ({account_info}, {proxy_info})")
                    processed_count += 1
                    continue
                response.raise_for_status()
                data = response.json()

                # 成功获取数据
                success_count += 1
                title = data.get('title', 'N/A')
                print(f"✅ Release {rid} fetched: {title} ({account_info}, {proxy_info})")

                # 转换API数据为数据库文档格式（传递数据库连接）
                release_doc = convert_api_data_to_document(data, db)
                if release_doc:
                    # 保存到数据库
                    if insert_release_to_database(db, release_doc):
                        saved_count += 1
                        print(f"💾 Release {rid} 已保存到数据库")
                    else:
                        print(f"⚠️ Release {rid} 保存到数据库失败")
                else:
                    print(f"⚠️ Release {rid} 数据转换失败")

            except Exception as e:
                print(f"⚠️ Error fetching release {rid}: {e} ({account_info}, {proxy_info})")

            processed_count += 1
            time.sleep(WAIT_TIME)

            # 显示进度
            if processed_count % 10 == 0:
                progress_pct = processed_count/len(missing_ids)*100
                print(f"📊 进度: {processed_count}/{len(missing_ids)} ({progress_pct:.1f}%)")

        print(f"🎉 处理完成！")
        print(f"   - 总处理: {processed_count} 个ID")
        print(f"   - 成功获取: {success_count} 条记录")
        print(f"   - 成功保存: {saved_count} 条记录到数据库")

    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
    finally:
        # 关闭数据库连接
        if 'client' in locals():
            client.close()
            print("🔒 数据库连接已关闭")

if __name__ == "__main__":
    main()
