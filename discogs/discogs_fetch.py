import requests
import time
import csv

TOKEN = 'zXwURCsYotIjXROUfIngknavKuMWCjcmODCXuEJs'
CSV_PATH = 'discogs_releases_100000_100200.csv'
WAIT_TIME = 1.05  # 控制请求速率，防止被限速

headers = {
    'User-Agent': 'DiscogsReleaseFetcher/1.0',
    'Authorization': f'Discogs token={TOKEN}'
}

with open(CSV_PATH, 'w', newline='', encoding='utf-8') as csvfile:
    writer = None
    for rid in range(100000, 100201):
        url = f'https://api.discogs.com/releases/{rid}'
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 404:
                print(f"❌ Release {rid} not found.")
                continue
            response.raise_for_status()
            data = response.json()

            # 初始化字段并写入表头
            if writer is None:
                writer = csv.DictWriter(csvfile, fieldnames=data.keys())
                writer.writeheader()

            writer.writerow(data)
            print(f"✅ Release {rid} fetched: {data.get('title', 'N/A')}")
        except Exception as e:
            print(f"⚠️ Error fetching release {rid}: {e}")
        time.sleep(WAIT_TIME)
