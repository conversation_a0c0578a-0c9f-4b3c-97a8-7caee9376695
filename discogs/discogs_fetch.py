import requests
import time
import csv

DISCOGS_ACCOUNTS = [
    {
        'email': '<EMAIL>',
        'token': 'KzOovyQCbHcFmnhUNjigyNCezgquqQYvTiQAgXsa',
        'user_agent': 'DiscogsAPIClient/1.0'
    },
    {
        'email': '<EMAIL>',
        'token': 'dYeSySUWrbjxXgVQbVNgKazNbrIRMHFcZgkpnlvj',
        'user_agent': 'DiscogsAPIClient/1.0'
    }
]

CSV_PATH = 'discogs_releases_100000_100200.csv'
WAIT_TIME = 1.05  # 控制请求速率，防止被限速

# 账号轮换计数器
account_counter = 0

def get_current_account():
    """获取当前轮换的账号信息"""
    global account_counter
    account = DISCOGS_ACCOUNTS[account_counter % len(DISCOGS_ACCOUNTS)]
    account_counter += 1
    return account

def get_headers(account):
    """根据账号信息生成请求头"""
    return {
        'User-Agent': account['user_agent'],
        'Authorization': f'Discogs token={account["token"]}'
    }

with open(CSV_PATH, 'w', newline='', encoding='utf-8') as csvfile:
    writer = None
    for rid in range(100000, 100201):
        # 获取当前轮换的账号
        current_account = get_current_account()
        headers = get_headers(current_account)

        url = f'https://api.discogs.com/releases/{rid}'
        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 404:
                print(f"❌ Release {rid} not found. (使用账号: {current_account['email']})")
                continue
            response.raise_for_status()
            data = response.json()

            # 初始化字段并写入表头
            if writer is None:
                writer = csv.DictWriter(csvfile, fieldnames=data.keys())
                writer.writeheader()

            writer.writerow(data)
            print(f"✅ Release {rid} fetched: {data.get('title', 'N/A')} (使用账号: {current_account['email']})")
        except Exception as e:
            print(f"⚠️ Error fetching release {rid}: {e} (使用账号: {current_account['email']})")
        time.sleep(WAIT_TIME)
